<?php $__env->startSection('title', isset($category) ? $category->name . ' - ShreeJi Jewelry' : 'Collections - ShreeJi Jewelry'); ?>
<?php $__env->startSection('description', isset($category) ? $category->description : 'Browse our complete collection of exquisite jewelry
    including rings, necklaces, earrings, and bracelets.'); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <section class="py-4 py-md-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <?php if(isset($category)): ?>
                        <h1 class="font-modern display-5 display-md-4 fw-bold mb-2 mb-md-3"><?php echo e($category->name); ?></h1>
                        <p class="lead text-muted d-none d-md-block"><?php echo e($category->description); ?></p>
                        <p class="text-muted d-md-none"><?php echo e(Str::limit($category->description, 80)); ?></p>
                    <?php elseif(isset($searchTerm)): ?>
                        <h1 class="font-modern display-5 display-md-4 fw-bold mb-2 mb-md-3">Search Results</h1>
                        <p class="lead text-muted d-none d-md-block">
                            <?php if($products->count() > 0): ?>
                                Found <?php echo e($products->total()); ?> results for "<?php echo e($searchTerm); ?>"
                            <?php else: ?>
                                No results found for "<?php echo e($searchTerm); ?>"
                            <?php endif; ?>
                        </p>
                        <p class="text-muted d-md-none">
                            <?php if($products->count() > 0): ?>
                                <?php echo e($products->total()); ?> results for "<?php echo e(Str::limit($searchTerm, 20)); ?>"
                            <?php else: ?>
                                No results found
                            <?php endif; ?>
                        </p>
                    <?php else: ?>
                        <h1 class="font-modern display-5 display-md-4 fw-bold mb-2 mb-md-3">Our Collections</h1>
                        <p class="lead text-muted d-none d-md-block">Discover our complete range of exquisite jewelry pieces
                        </p>
                        <p class="text-muted d-md-none">Exquisite jewelry pieces</p>
                    <?php endif; ?>

                    <!-- Breadcrumb -->
                    <nav aria-label="breadcrumb" class="d-none d-md-block">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a href="<?php echo e(url('/')); ?>">Home</a></li>
                            <?php if(isset($category)): ?>
                                <li class="breadcrumb-item"><a href="<?php echo e(route('collections')); ?>">Collections</a></li>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo e($category->name); ?></li>
                            <?php else: ?>
                                <li class="breadcrumb-item active" aria-current="page">Collections</li>
                            <?php endif; ?>
                        </ol>
                    </nav>

                    <!-- Mobile Breadcrumb -->
                    <nav aria-label="breadcrumb" class="d-md-none">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="<?php echo e(url('/')); ?>"><i class="fas fa-home"></i></a></li>
                            <?php if(isset($category)): ?>
                                <li class="breadcrumb-item"><a href="<?php echo e(route('collections')); ?>">Collections</a></li>
                                <li class="breadcrumb-item active" aria-current="page">
                                    <?php echo e(Str::limit($category->name, 15)); ?></li>
                            <?php else: ?>
                                <li class="breadcrumb-item active" aria-current="page">Collections</li>
                            <?php endif; ?>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Filter Section -->
    <section class="py-3 py-md-4 border-bottom bg-white sticky-top" style="top: var(--navbar-height); z-index: 100;">
        <div class="container">
            <!-- Mobile Filter Layout -->
            <div class="d-md-none">
                <div class="row g-2">
                    <div class="col-4">
                        <div class="dropdown w-100">
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle w-100" type="button"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-filter me-1"></i>
                                <?php if(isset($category)): ?>
                                    <?php echo e($category->name); ?>

                                <?php else: ?>
                                    Category
                                <?php endif; ?>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item <?php echo e(!isset($category) ? 'active' : ''); ?>" href="<?php echo e(route('collections')); ?>">All Categories</a></li>
                                <?php if(isset($globalCategories)): ?>
                                    <?php $__currentLoopData = $globalCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li><a class="dropdown-item <?php echo e(isset($category) && $category->slug === $cat->slug ? 'active' : ''); ?>"
                                                href="<?php echo e(route('collections.category', $cat->slug)); ?>"><?php echo e($cat->name); ?></a>
                                        </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="dropdown w-100">
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle w-100" type="button"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-rupee-sign me-1"></i>Price
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item <?php echo e(!request('min_price') && !request('max_price') ? 'active' : ''); ?>"
                                        href="<?php echo e(request()->fullUrlWithQuery(['min_price' => null, 'max_price' => null])); ?>">All
                                        Prices</a></li>
                                <li><a class="dropdown-item <?php echo e(!request('min_price') && request('max_price') == 10000 ? 'active' : ''); ?>"
                                        href="<?php echo e(request()->fullUrlWithQuery(['min_price' => null, 'max_price' => 10000])); ?>">Under
                                        ₹10K</a></li>
                                <li><a class="dropdown-item <?php echo e(request('min_price') == 10000 && request('max_price') == 25000 ? 'active' : ''); ?>"
                                        href="<?php echo e(request()->fullUrlWithQuery(['min_price' => 10000, 'max_price' => 25000])); ?>">₹10K
                                        - ₹25K</a></li>
                                <li><a class="dropdown-item <?php echo e(request('min_price') == 25000 && request('max_price') == 50000 ? 'active' : ''); ?>"
                                        href="<?php echo e(request()->fullUrlWithQuery(['min_price' => 25000, 'max_price' => 50000])); ?>">₹25K
                                        - ₹50K</a></li>
                                <li><a class="dropdown-item <?php echo e(request('min_price') == 50000 && !request('max_price') ? 'active' : ''); ?>"
                                        href="<?php echo e(request()->fullUrlWithQuery(['min_price' => 50000, 'max_price' => null])); ?>">Above
                                        ₹50K</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-4">
                        <select class="form-select form-select-sm" onchange="window.location.href = this.value">
                            <option value="<?php echo e(request()->fullUrlWithQuery(['sort' => 'featured'])); ?>"
                                <?php echo e(request('sort') == 'featured' ? 'selected' : ''); ?>>Featured</option>
                            <option value="<?php echo e(request()->fullUrlWithQuery(['sort' => 'price_low'])); ?>"
                                <?php echo e(request('sort') == 'price_low' ? 'selected' : ''); ?>>Price ↑</option>
                            <option value="<?php echo e(request()->fullUrlWithQuery(['sort' => 'price_high'])); ?>"
                                <?php echo e(request('sort') == 'price_high' ? 'selected' : ''); ?>>Price ↓</option>
                            <option value="<?php echo e(request()->fullUrlWithQuery(['sort' => 'newest'])); ?>"
                                <?php echo e(request('sort') == 'newest' ? 'selected' : ''); ?>>Newest</option>
                            <option value="<?php echo e(request()->fullUrlWithQuery(['sort' => 'name'])); ?>"
                                <?php echo e(request('sort') == 'name' ? 'selected' : ''); ?>>A-Z</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Desktop Filter Layout -->
            <div class="row align-items-center d-none d-md-flex">
                <div class="col-md-6">
                    <div class="d-flex align-items-center gap-3">
                        <span class="fw-semibold">Filter by:</span>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                <?php if(isset($category)): ?>
                                    <?php echo e($category->name); ?>

                                <?php else: ?>
                                    Category
                                <?php endif; ?>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item <?php echo e(!isset($category) ? 'active' : ''); ?>" href="<?php echo e(route('collections')); ?>">All Categories</a></li>
                                <?php if(isset($globalCategories)): ?>
                                    <?php $__currentLoopData = $globalCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li><a class="dropdown-item <?php echo e(isset($category) && $category->slug === $cat->slug ? 'active' : ''); ?>"
                                                href="<?php echo e(route('collections.category', $cat->slug)); ?>"><?php echo e($cat->name); ?></a>
                                        </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </ul>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                Price Range
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item <?php echo e(!request('min_price') && !request('max_price') ? 'active' : ''); ?>"
                                        href="<?php echo e(request()->fullUrlWithQuery(['min_price' => null, 'max_price' => null])); ?>">All
                                        Prices</a></li>
                                <li><a class="dropdown-item <?php echo e(!request('min_price') && request('max_price') == 10000 ? 'active' : ''); ?>"
                                        href="<?php echo e(request()->fullUrlWithQuery(['min_price' => null, 'max_price' => 10000])); ?>">Under
                                        ₹10,000</a></li>
                                <li><a class="dropdown-item <?php echo e(request('min_price') == 10000 && request('max_price') == 25000 ? 'active' : ''); ?>"
                                        href="<?php echo e(request()->fullUrlWithQuery(['min_price' => 10000, 'max_price' => 25000])); ?>">₹10,000
                                        - ₹25,000</a></li>
                                <li><a class="dropdown-item <?php echo e(request('min_price') == 25000 && request('max_price') == 50000 ? 'active' : ''); ?>"
                                        href="<?php echo e(request()->fullUrlWithQuery(['min_price' => 25000, 'max_price' => 50000])); ?>">₹25,000
                                        - ₹50,000</a></li>
                                <li><a class="dropdown-item <?php echo e(request('min_price') == 50000 && !request('max_price') ? 'active' : ''); ?>"
                                        href="<?php echo e(request()->fullUrlWithQuery(['min_price' => 50000, 'max_price' => null])); ?>">Above
                                        ₹50,000</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex align-items-center justify-content-md-end gap-3">
                        <span class="fw-semibold">Sort by:</span>
                        <select class="form-select" style="width: auto;" onchange="window.location.href = this.value">
                            <option value="<?php echo e(request()->fullUrlWithQuery(['sort' => 'featured'])); ?>"
                                <?php echo e(request('sort') == 'featured' ? 'selected' : ''); ?>>Featured</option>
                            <option value="<?php echo e(request()->fullUrlWithQuery(['sort' => 'price_low'])); ?>"
                                <?php echo e(request('sort') == 'price_low' ? 'selected' : ''); ?>>Price: Low to High</option>
                            <option value="<?php echo e(request()->fullUrlWithQuery(['sort' => 'price_high'])); ?>"
                                <?php echo e(request('sort') == 'price_high' ? 'selected' : ''); ?>>Price: High to Low</option>
                            <option value="<?php echo e(request()->fullUrlWithQuery(['sort' => 'newest'])); ?>"
                                <?php echo e(request('sort') == 'newest' ? 'selected' : ''); ?>>Newest First</option>
                            <option value="<?php echo e(request()->fullUrlWithQuery(['sort' => 'name'])); ?>"
                                <?php echo e(request('sort') == 'name' ? 'selected' : ''); ?>>Name A-Z</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Products Grid -->
    <section class="py-5">
        <div class="container">
            <?php if($products->count() > 0): ?>
                <div class="row g-4 g-md-4">
                    <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-12 col-md-6 col-lg-4 col-xl-3">
                            <div class="card product-card h-100 <?php echo e(!$product->in_stock ? 'out-of-stock' : ''); ?>">
                                <div class="position-relative overflow-hidden product-image-container">
                                    <?php if($product->images && count($product->images) > 0): ?>
                                        <?php
                                            $imagePath = $product->images[0];
                                            // Check if it's a full URL
                                            if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
                                                $imageUrl = $imagePath;
                                            } else {
                                                // Clean the path and ensure storage/ prefix
                                                $cleanPath = str_replace(['\\/', '\\'], '/', $imagePath);
                                                // Add storage/ prefix if not already present
                                                if (!str_starts_with($cleanPath, 'storage/')) {
                                                    $cleanPath = 'storage/' . ltrim($cleanPath, '/');
                                                }
                                                $imageUrl = asset($cleanPath);
                                            }
                                        ?>
                                        <img src="<?php echo e($imageUrl); ?>"
                                             class="card-img-top <?php echo e(!$product->in_stock ? 'opacity-50' : ''); ?>"
                                             alt="<?php echo e($product->name); ?>"
                                             style="height: 250px; object-fit: cover;"
                                             onerror="this.src='https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'">
                                    <?php else: ?>
                                        <img src="https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                                             class="card-img-top <?php echo e(!$product->in_stock ? 'opacity-50' : ''); ?>"
                                             alt="<?php echo e($product->name); ?>"
                                             style="height: 250px; object-fit: cover;">
                                    <?php endif; ?>

                                    <?php if(!$product->in_stock): ?>
                                        <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background: rgba(0,0,0,0.3);">
                                            <span class="badge bg-secondary fs-6 px-3 py-2">
                                                <i class="fas fa-times me-1"></i>Out of Stock
                                            </span>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Hover hint for desktop -->
                                    <!-- Removed overlay - keeping only subtle hover effects -->

                                    <?php if($product->isOnSale()): ?>
                                        <span class="badge bg-danger position-absolute top-0 start-0 m-2">
                                            <?php echo e($product->discount_percentage); ?>% OFF
                                        </span>
                                    <?php elseif($product->is_featured): ?>
                                        <span
                                            class="badge bg-primary-brown position-absolute top-0 start-0 m-2">Featured</span>
                                    <?php endif; ?>
                                </div>
                                <div class="card-body text-center d-flex flex-column">
                                    <h5 class="card-title font-modern"><?php echo e($product->name); ?></h5>
                                    <p class="card-text text-muted small"><?php echo e($product->category->name); ?></p>
                                    <div class="d-flex justify-content-center align-items-center gap-2 mb-2">
                                        <?php if($product->isOnSale()): ?>
                                            <span
                                                class="fw-bold fs-5 font-modern" style="color: var(--primary-brown);">₹<?php echo e(number_format($product->sale_price)); ?></span>
                                            <span
                                                class="text-muted text-decoration-line-through small">₹<?php echo e(number_format($product->price)); ?></span>
                                        <?php else: ?>
                                            <span
                                                class="fw-bold fs-5 font-modern" style="color: var(--primary-brown);">₹<?php echo e(number_format($product->price)); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <?php if($product->metal_type || $product->stone_type): ?>
                                        <div class="mt-auto mb-3">
                                            <?php if($product->metal_type): ?>
                                                <small
                                                    class="badge bg-light text-dark me-1"><?php echo e($product->metal_type); ?></small>
                                            <?php endif; ?>
                                            <?php if($product->stone_type): ?>
                                                <small class="badge bg-light text-dark"><?php echo e($product->stone_type); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Clear Add to Cart Button for All Devices -->
                                    <div class="mt-auto">
                                        <?php if($product->in_stock): ?>
                                            <button class="btn btn-primary-pink w-100 mb-2 add-to-cart-btn"
                                                data-product-id="<?php echo e($product->id); ?>"
                                                style="font-weight: 600;">
                                                <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                                            </button>
                                            <div class="d-flex gap-2">
                                                <a href="<?php echo e(route('product.detail', $product->slug)); ?>"
                                                   class="btn btn-outline-primary btn-sm w-100">
                                                    <i class="fas fa-eye me-1"></i>View Details
                                                </a>
                                            </div>
                                        <?php else: ?>
                                            <button class="btn btn-secondary w-100 mb-2" disabled>
                                                <i class="fas fa-times me-2"></i>Out of Stock
                                            </button>
                                            <div class="d-flex gap-2">
                                                <a href="<?php echo e(route('product.detail', $product->slug)); ?>"
                                                   class="btn btn-outline-primary btn-sm w-100">
                                                    <i class="fas fa-eye me-1"></i>View Details
                                                </a>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>


                <!-- Pagination -->
                <?php if($products->hasPages()): ?>
                    <div class="row mt-5">
                        <div class="col-12">
                            <nav aria-label="Products pagination">
                                <?php echo e($products->appends(request()->query())->links('pagination::bootstrap-5')); ?>

                            </nav>
                        </div>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <!-- No Products Found -->
                <div class="row">
                    <div class="col-12 text-center py-5">
                        <i class="fas fa-gem text-muted mb-4" style="font-size: 4rem;"></i>
                        <h3 class="font-modern mb-3">No Products Found</h3>
                        <p class="text-muted mb-4">
                            <?php if(isset($category)): ?>
                                No products found in <?php echo e($category->name); ?> category.
                            <?php elseif(isset($searchTerm)): ?>
                                No products found for "<?php echo e($searchTerm); ?>".
                            <?php else: ?>
                                No products found matching your criteria.
                            <?php endif; ?>
                        </p>
                        <a href="<?php echo e(route('collections')); ?>" class="btn btn-primary-pink">
                            <i class="fas fa-arrow-left me-2"></i>View All Products
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
    <style>
        .breadcrumb-item+.breadcrumb-item::before {
            color: var(--primary-brown);
        }

        .breadcrumb-item a {
            color: var(--primary-brown);
            text-decoration: none;
        }

        .breadcrumb-item a:hover {
            text-decoration: underline;
        }

        .pagination .page-link {
            color: var(--primary-brown);
            border-color: var(--primary-brown);
        }

        .pagination .page-item.active .page-link {
            background-color: var(--primary-brown);
            border-color: var(--primary-brown);
        }

        .pagination .page-link:hover {
            background-color: var(--secondary-brown);
            border-color: var(--secondary-brown);
            color: white;
        }

        .product-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            overflow: hidden;
            background: #fff;
            height: 100%;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
        }

        .product-image-container {
            height: 250px;
            overflow: hidden;
            position: relative;
            background: #f8f9fa;
            border-radius: 12px 12px 0 0;
        }

        .card-img-top {
            height: 100%;
            width: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .product-card:hover .card-img-top {
            transform: scale(1.05);
        }

        /* Hover hint styling */
        .hover-hint {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1;
            opacity: 0.8;
            transition: opacity 0.3s ease;
        }

        .product-card:hover .hover-hint {
            opacity: 0;
        }

        /* Removed product overlay - keeping only subtle hover effects */

        /* Mobile-First Collections Page Improvements */

        /* Mobile Header Adjustments */
        @media (max-width: 767.98px) {
            .display-5 {
                font-size: 2rem !important;
            }

            .breadcrumb {
                font-size: 0.85rem;
            }

            .breadcrumb-item+.breadcrumb-item::before {
                font-size: 0.75rem;
            }
        }

        /* Mobile Filter Section */
        .sticky-top {
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        @media (max-width: 767.98px) {
            .btn-sm {
                font-size: 0.8rem;
                padding: 0.375rem 0.5rem;
            }

            .form-select-sm {
                font-size: 0.8rem;
                padding: 0.375rem 0.5rem;
            }
        }

        /* Mobile Product Grid - Vertical Layout */
        @media (max-width: 575.98px) {
            .product-card {
                border-radius: 16px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                transition: transform 0.2s ease, box-shadow 0.2s ease;
                overflow: hidden;
            }

            .product-card:hover {
                transform: translateY(-4px);
                box-shadow: 0 8px 24px rgba(0,0,0,0.15);
            }

            .product-image-container {
                height: auto !important;
                aspect-ratio: 1/1;
                min-height: 0;
            }
            .product-image-container img.card-img-top {
                width: 100% !important;
                height: 100% !important;
                object-fit: cover !important;
                display: block;
            }

            .card-body {
                padding: 1rem;
                text-align: center;
            }

            .card-title {
                font-size: 1.1rem;
                margin-bottom: 0.5rem;
                line-height: 1.3;
                font-weight: 600;
                color: #2c3e50;
            }

            .card-text {
                font-size: 0.85rem;
                margin-bottom: 0.75rem;
                color: #6c757d;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .fs-5 {
                font-size: 1.25rem !important;
                font-weight: 700;
                color: var(--primary-brown);
            }

            .badge {
                font-size: 0.75rem;
                padding: 0.375rem 0.75rem;
                border-radius: 20px;
                margin: 0.125rem;
            }

            /* Mobile action buttons */
            .mobile-add-cart-btn,
            .buy-now-btn-mobile {
                font-size: 0.9rem;
                padding: 0.75rem 1rem;
                border-radius: 25px;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .mobile-add-cart-btn {
                background: linear-gradient(135deg, #f97bcbe1, #ff3c90);
                border: none;
                color: white;
            }

            .buy-now-btn-mobile {
                background: linear-gradient(135deg, #28a745, #20c997);
                border: none;
                color: white;
            }

            /* Product overlay removed */

            /* Adjust spacing */
            .row.g-4 {
                --bs-gutter-y: 1.5rem;
            }

            /* Enhanced typography */
            .card-title {
                font-family: 'Playfair Display', serif;
                font-weight: 600;
                color: #2c3e50;
                line-height: 1.3;
            }

            .card-text {
                color: #6c757d;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                font-weight: 500;
            }

            /* Enhanced price styling */
            .text-primary-brown {
                color: var(--primary-brown) !important;
                font-weight: 700;
            }

            /* Mobile-specific badge positioning */
            .badge.position-absolute {
                top: 1rem !important;
                left: 1rem !important;
                z-index: 2;
                font-weight: 600;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            }

            /* Price section styling */
            .d-flex.justify-content-center.justify-content-sm-start {
                justify-content: center !important;
                margin-bottom: 1rem;
            }

            /* Badge container styling */
            .mt-auto {
                margin-top: 0.75rem !important;
            }

            /* Mobile button container */
            .d-flex.gap-2.mt-3.d-md-none {
                margin-top: 1rem !important;
                gap: 0.75rem !important;
            }

            /* Enhanced mobile typography */
            .font-modern {
                font-family: 'Poppins', 'Inter', sans-serif;
                font-weight: 600;
            }

            /* Better mobile card spacing */
            .container .row.g-3.g-md-4 {
                margin: 0 -0.5rem;
            }

            .container .row.g-3.g-md-4 > .col-12 {
                padding: 0 0.5rem;
            }
        }

        /* Tablet Product Grid */
        @media (min-width: 576px) and (max-width: 767.98px) {
            .product-card {
                border-radius: 12px;
                box-shadow: 0 3px 10px rgba(0,0,0,0.1);
                transition: transform 0.2s ease, box-shadow 0.2s ease;
            }

            .product-card:hover {
                transform: translateY(-3px);
                box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            }

            .product-image-container {
                height: 220px;
                border-radius: 12px 12px 0 0;
                overflow: hidden;
            }

            .product-image-container img {
                transition: transform 0.3s ease;
            }

            .product-card:hover .product-image-container img {
                transform: scale(1.03);
            }

            .card-body {
                padding: 1rem;
            }

            .card-title {
                font-size: 1.05rem;
                font-weight: 600;
                margin-bottom: 0.5rem;
            }

            .card-text {
                font-size: 0.9rem;
                margin-bottom: 0.75rem;
            }

            .fs-5 {
                font-size: 1.15rem !important;
                font-weight: 700;
            }
        }

        /* Overlay removed */

        /* Desktop Product Grid */
        @media (min-width: 768px) {
            .product-card {
                border-radius: 12px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.08);
                transition: all 0.3s ease;
                overflow: hidden;
            }

            .product-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            }

            .product-image-container {
                height: 280px;
                border-radius: 12px 12px 0 0;
                overflow: hidden;
                position: relative;
            }

            .product-image-container img {
                transition: transform 0.4s ease;
            }

            .product-card:hover .product-image-container img {
                transform: scale(1.08);
            }

            .card-body {
                padding: 1.25rem;
            }

            .card-title {
                font-size: 1.15rem;
                font-weight: 600;
                margin-bottom: 0.5rem;
                color: #2c3e50;
            }

            .card-text {
                font-size: 0.9rem;
                margin-bottom: 0.75rem;
                color: #6c757d;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .fs-5 {
                font-size: 1.3rem !important;
                font-weight: 700;
                color: var(--primary-brown);
            }

            /* Removed desktop overlay styles */

            /* Badge styling for desktop */
            .badge.position-absolute {
                top: 1rem;
                left: 1rem;
                font-weight: 600;
                border-radius: 20px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            }

            .mobile-add-cart-btn,
            .buy-now-btn-mobile {
                display: none !important;
            }
        }

        /* Mobile pagination */
        @media (max-width: 767.98px) {
            .pagination {
                justify-content: center;
            }

            .page-link {
                padding: 0.5rem 0.75rem;
                font-size: 0.9rem;
            }
        }

        /* Mobile empty state */
        @media (max-width: 767.98px) {
            .py-5 {
                padding: 2rem 0 !important;
            }

            .text-center h3 {
                font-size: 1.5rem;
            }

            .text-center p {
                font-size: 0.9rem;
            }
        }

        /* Touch-friendly improvements */
        @media (max-width: 767.98px) {

            .dropdown-toggle,
            .form-select,
            .btn {
                min-height: 44px;
            }

            .dropdown-item {
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
            }
        }

        /* Performance optimizations for mobile */
        @media (max-width: 767.98px) {
            .product-card .card-img-top {
                will-change: auto;
            }

            .product-card:hover .card-img-top {
                transform: none;
            }
        }

        /* Out of stock styling */
        .product-card.out-of-stock {
            position: relative;
        }

        .product-card.out-of-stock .card-body {
            opacity: 0.7;
        }

        /* Overlay removed */

        /* Enhanced Add to Cart Button Animations */
        .add-to-cart-btn {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .add-to-cart-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .add-to-cart-btn:active {
            transform: translateY(0);
        }

        /* Accessibility improvements */
        @media (prefers-reduced-motion: reduce) {
            .product-card,
            .product-card:hover,
            .add-to-cart-btn,
            .add-to-cart-btn:hover {
                transform: none;
                transition: none;
            }
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        // Add to cart functionality
        document.querySelectorAll('.add-to-cart-btn').forEach(button => {
            button.addEventListener('click', function() {
                const productId = this.dataset.productId;

                // Add loading state with animation
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Adding...';
                this.disabled = true;
                this.style.transform = 'scale(0.95)';

                fetch('/cart/add', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                .getAttribute('content'),
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                        },
                        body: JSON.stringify({
                            product_id: productId,
                            quantity: 1
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Show enhanced success notification with actions
                            if (typeof showEnhancedNotification === 'function') {
                                showEnhancedNotification('Product added to cart successfully!', 'success', { showActions: true });
                            } else {
                                showNotification('Product added to cart!', 'success');
                            }

                            // Update cart count if available
                            if (data.cart_count) {
                                updateCartCount(data.cart_count);
                            }

                            // Success animation
                            this.innerHTML = '<i class="fas fa-check me-1"></i>Added!';
                            this.classList.remove('btn-primary-pink');
                            this.classList.add('btn-success');

                            setTimeout(() => {
                                this.innerHTML = originalText;
                                this.classList.remove('btn-success');
                                this.classList.add('btn-primary-pink');
                            }, 2000);
                        } else {
                            let errorMessage = data.message || 'Error adding product to cart';
                            if (errorMessage.includes('out of stock') || errorMessage.includes('insufficient')) {
                                errorMessage = 'Sorry, this product is currently out of stock or has insufficient quantity available.';
                            }
                            if (typeof showEnhancedNotification === 'function') {
                                showEnhancedNotification(errorMessage, 'error');
                            } else {
                                showNotification(errorMessage, 'error');
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        const errorMsg = 'Unable to add product to cart. Please check your connection and try again.';
                        if (typeof showEnhancedNotification === 'function') {
                            showEnhancedNotification(errorMsg, 'error');
                        } else {
                            showNotification(errorMsg, 'error');
                        }
                    })
                    .finally(() => {
                        // Restore button state
                        if (!this.classList.contains('btn-success')) {
                            this.innerHTML = originalText;
                        }
                        this.disabled = false;
                        this.style.transform = 'scale(1)';
                    });
            });
        });

        // Buy Now functionality
        document.querySelectorAll('.buy-now-btn, .buy-now-btn-mobile').forEach(button => {
            button.addEventListener('click', function() {
                const productId = this.dataset.productId;

                // Add loading state
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing...';
                this.disabled = true;

                fetch('/cart/add', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                .getAttribute('content'),
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                        },
                        body: JSON.stringify({
                            product_id: productId,
                            quantity: 1,
                            buy_now: true
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Redirect to checkout
                            window.location.href = '/checkout';
                        } else {
                            // Restore button state
                            this.innerHTML = originalText;
                            this.disabled = false;
                            showNotification(data.message || 'Error processing request', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        // Restore button state
                        this.innerHTML = originalText;
                        this.disabled = false;
                        showNotification('Error processing request', 'error');
                    });
            });
        });



        // Add to cart functionality for mobile buttons
        document.querySelectorAll('.add-to-cart-btn-mobile').forEach(button => {
            button.addEventListener('click', function() {
                const productId = this.dataset.productId;

                fetch('/cart/add', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                .getAttribute('content'),
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                        },
                        body: JSON.stringify({
                            product_id: productId,
                            quantity: 1
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showNotification('Product added to cart!', 'success');
                            if (data.cart_count) {
                                updateCartCount(data.cart_count);
                            }
                        } else {
                            showNotification(data.message || 'Error adding product to cart', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showNotification('Error adding product to cart', 'error');
                    });
            });
        });

        // Utility functions
        function showNotification(message, type) {
            // Remove any existing notifications first
            document.querySelectorAll('.cart-notification').forEach(notification => {
                notification.remove();
            });

            // Create notification element
            const notification = document.createElement('div');
            notification.className =
                `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed cart-notification`;
            notification.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 350px; max-width: 400px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); border-radius: 8px;';

            const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';
            const title = type === 'success' ? 'Success!' : 'Error!';

            notification.innerHTML = `
                <div class="d-flex align-items-start">
                    <i class="${icon} me-2" style="font-size: 1.1rem; margin-top: 2px;"></i>
                    <div class="flex-grow-1">
                        <div class="fw-semibold mb-1">${title}</div>
                        <div class="small">${message}</div>
                    </div>
                    <button type="button" class="btn-close ms-2" data-bs-dismiss="alert"></button>
                </div>
            `;

            document.body.appendChild(notification);

            // No auto-hide - user must manually close the notification
        }

        function updateCartCount(count) {
            const cartCountElements = document.querySelectorAll('.cart-count');
            cartCountElements.forEach(element => {
                element.textContent = count;
            });
        }

        // New Add to Cart function for mobile buttons
        function addToCartMobile(productId) {
            fetch('/cart/add', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    product_id: productId,
                    quantity: 1
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Product added to cart!', 'success');
                    if (data.cart_count) {
                        updateCartCount(data.cart_count);
                    }
                } else {
                    let errorMessage = data.message || 'Error adding product to cart';
                    if (errorMessage.includes('out of stock') || errorMessage.includes('insufficient')) {
                        errorMessage = 'Sorry, this product is currently out of stock or has insufficient quantity available.';
                    }
                    showNotification(errorMessage, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Unable to add product to cart. Please check your connection and try again.', 'error');
            });
        }

        // Initialize Bootstrap dropdowns
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize all dropdowns
            var dropdownElementList = [].slice.call(document.querySelectorAll('[data-bs-toggle="dropdown"]'));
            var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
                return new bootstrap.Dropdown(dropdownToggleEl);
            });

            // Debug: Log dropdown initialization
            console.log('Initialized', dropdownList.length, 'dropdowns');

            // Add click event listeners to dropdown buttons for debugging
            dropdownElementList.forEach(function(element) {
                element.addEventListener('click', function(e) {
                    console.log('Dropdown clicked:', this);
                    // Ensure the dropdown is properly toggled
                    if (!this.getAttribute('aria-expanded') || this.getAttribute('aria-expanded') === 'false') {
                        this.setAttribute('aria-expanded', 'true');
                    } else {
                        this.setAttribute('aria-expanded', 'false');
                    }
                });
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\shreejimarg-main\resources\views/collections.blade.php ENDPATH**/ ?>